#!/bin/bash
# Apache SSL框架通用搭建脚本
# 系统: CentOS 9 / RHEL 9
# 功能: 通用Apache + SSL证书配置框架

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置变量（可通过参数覆盖）
DEFAULT_DOMAIN="example.com"
DEFAULT_IP="*************"
DEFAULT_WEBROOT="/var/www/html"
DEFAULT_SSL_COUNTRY="CN"
DEFAULT_SSL_STATE="Beijing"
DEFAULT_SSL_CITY="Beijing"
DEFAULT_SSL_ORG="Example Organization"
DEFAULT_SSL_UNIT="IT Department"

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "Apache SSL框架搭建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -d, --domain DOMAIN     设置域名 (默认: $DEFAULT_DOMAIN)"
    echo "  -i, --ip IP            设置IP地址 (默认: $DEFAULT_IP)"
    echo "  -w, --webroot PATH     设置网站根目录 (默认: $DEFAULT_WEBROOT)"
    echo "  -c, --country CODE     设置SSL证书国家代码 (默认: $DEFAULT_SSL_COUNTRY)"
    echo "  -s, --state STATE      设置SSL证书省份 (默认: $DEFAULT_SSL_STATE)"
    echo "  -l, --city CITY        设置SSL证书城市 (默认: $DEFAULT_SSL_CITY)"
    echo "  -o, --org ORG          设置SSL证书组织 (默认: $DEFAULT_SSL_ORG)"
    echo "  -u, --unit UNIT        设置SSL证书部门 (默认: $DEFAULT_SSL_UNIT)"
    echo "  -h, --help             显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -d nuli.cn -i *************"
    echo "  $0 --domain example.com --ip ********** --webroot /var/www/mysite"
    echo ""
}

# 检查是否以root用户运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root用户运行此脚本"
        exit 1
    fi
}

# 解析命令行参数
parse_arguments() {
    DOMAIN="$DEFAULT_DOMAIN"
    SERVER_IP="$DEFAULT_IP"
    WEBROOT="$DEFAULT_WEBROOT"
    SSL_COUNTRY="$DEFAULT_SSL_COUNTRY"
    SSL_STATE="$DEFAULT_SSL_STATE"
    SSL_CITY="$DEFAULT_SSL_CITY"
    SSL_ORG="$DEFAULT_SSL_ORG"
    SSL_UNIT="$DEFAULT_SSL_UNIT"

    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--domain)
                DOMAIN="$2"
                shift 2
                ;;
            -i|--ip)
                SERVER_IP="$2"
                shift 2
                ;;
            -w|--webroot)
                WEBROOT="$2"
                shift 2
                ;;
            -c|--country)
                SSL_COUNTRY="$2"
                shift 2
                ;;
            -s|--state)
                SSL_STATE="$2"
                shift 2
                ;;
            -l|--city)
                SSL_CITY="$2"
                shift 2
                ;;
            -o|--org)
                SSL_ORG="$2"
                shift 2
                ;;
            -u|--unit)
                SSL_UNIT="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# 显示配置信息
show_config() {
    print_header "Apache SSL框架配置信息："
    print_info "域名: $DOMAIN"
    print_info "IP地址: $SERVER_IP"
    print_info "网站根目录: $WEBROOT"
    print_info "SSL证书信息:"
    print_info "  - 国家: $SSL_COUNTRY"
    print_info "  - 省份: $SSL_STATE"
    print_info "  - 城市: $SSL_CITY"
    print_info "  - 组织: $SSL_ORG"
    print_info "  - 部门: $SSL_UNIT"
    echo ""

    read -p "确认使用以上配置? (y/n): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        print_error "用户取消配置"
        exit 1
    fi
}

# 检测系统类型
detect_system() {
    if [ -f /etc/redhat-release ]; then
        if grep -q "CentOS\|Red Hat" /etc/redhat-release; then
            SYSTEM_TYPE="rhel"
            PACKAGE_MANAGER="dnf"
            print_info "检测到RHEL/CentOS系统"
        fi
    elif [ -f /etc/debian_version ]; then
        SYSTEM_TYPE="debian"
        PACKAGE_MANAGER="apt"
        print_info "检测到Debian/Ubuntu系统"
    else
        print_error "不支持的系统类型"
        exit 1
    fi
}

# 安装Apache和SSL模块
install_apache_ssl() {
    print_header "安装Apache和SSL模块..."

    case $SYSTEM_TYPE in
        "rhel")
            # 更新系统
            $PACKAGE_MANAGER update -y

            # 安装Apache和SSL模块
            if ! $PACKAGE_MANAGER install -y httpd mod_ssl openssl; then
                print_error "Apache和SSL模块安装失败"
                exit 1
            fi

            APACHE_SERVICE="httpd"
            APACHE_CONFIG_DIR="/etc/httpd/conf.d"
            APACHE_USER="apache"
            ;;
        "debian")
            # 更新系统
            $PACKAGE_MANAGER update -y

            # 安装Apache和SSL模块
            if ! $PACKAGE_MANAGER install -y apache2 openssl; then
                print_error "Apache和SSL模块安装失败"
                exit 1
            fi

            # 启用SSL模块
            a2enmod ssl
            a2enmod rewrite
            a2enmod headers

            APACHE_SERVICE="apache2"
            APACHE_CONFIG_DIR="/etc/apache2/sites-available"
            APACHE_USER="www-data"
            ;;
    esac

    print_success "Apache和SSL模块安装完成"
}

# 生成SSL证书
generate_ssl_certificate() {
    print_header "生成SSL证书..."

    # 创建SSL证书目录
    case $SYSTEM_TYPE in
        "rhel")
            SSL_CERT_DIR="/etc/pki/tls/certs"
            SSL_KEY_DIR="/etc/pki/tls/private"
            ;;
        "debian")
            SSL_CERT_DIR="/etc/ssl/certs"
            SSL_KEY_DIR="/etc/ssl/private"
            ;;
    esac

    mkdir -p $SSL_CERT_DIR
    mkdir -p $SSL_KEY_DIR

    # 检查SSL证书是否已存在
    if [ -f "$SSL_CERT_DIR/$DOMAIN.crt" ] && [ -s "$SSL_CERT_DIR/$DOMAIN.crt" ]; then
        print_info "SSL证书已存在，跳过生成"
        return 0
    fi

    print_info "生成SSL私钥..."
    openssl genrsa -out $SSL_KEY_DIR/$DOMAIN.key 2048

    print_info "生成证书签名请求..."
    openssl req -new -key $SSL_KEY_DIR/$DOMAIN.key -out /tmp/$DOMAIN.csr -subj "/C=$SSL_COUNTRY/ST=$SSL_STATE/L=$SSL_CITY/O=$SSL_ORG/OU=$SSL_UNIT/CN=$DOMAIN/emailAddress=admin@$DOMAIN"

    print_info "生成自签名证书..."
    openssl x509 -req -days 365 -in /tmp/$DOMAIN.csr -signkey $SSL_KEY_DIR/$DOMAIN.key -out $SSL_CERT_DIR/$DOMAIN.crt

    # 设置权限
    chmod 600 $SSL_KEY_DIR/$DOMAIN.key
    chmod 644 $SSL_CERT_DIR/$DOMAIN.crt

    # 清理临时文件
    rm -f /tmp/$DOMAIN.csr

    # 验证证书
    if openssl x509 -in $SSL_CERT_DIR/$DOMAIN.crt -text -noout > /dev/null 2>&1; then
        print_success "SSL证书生成并验证成功"
    else
        print_error "SSL证书验证失败"
        exit 1
    fi
}

# 配置Apache虚拟主机
configure_apache_vhosts() {
    print_header "配置Apache虚拟主机..."

    # 创建网站目录
    mkdir -p $WEBROOT

    case $SYSTEM_TYPE in
        "rhel")
            configure_rhel_vhosts
            ;;
        "debian")
            configure_debian_vhosts
            ;;
    esac

    print_success "Apache虚拟主机配置完成"
}

# 配置RHEL/CentOS虚拟主机
configure_rhel_vhosts() {
    # HTTP重定向配置
    cat > $APACHE_CONFIG_DIR/ssl-redirect.conf << EOF
<VirtualHost *:80>
    ServerName $DOMAIN
    ServerAlias $SERVER_IP
    ServerAlias localhost

    # 重定向所有HTTP请求到HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

    ErrorLog /var/log/httpd/ssl-redirect-error.log
    CustomLog /var/log/httpd/ssl-redirect-access.log combined
</VirtualHost>
EOF

    # HTTPS虚拟主机配置
    cat > $APACHE_CONFIG_DIR/ssl-website.conf << EOF
<VirtualHost *:443>
    ServerName $DOMAIN
    ServerAlias $SERVER_IP
    DocumentRoot $WEBROOT
    DirectoryIndex index.html index.php

    # SSL配置
    SSLEngine on
    SSLCertificateFile $SSL_CERT_DIR/$DOMAIN.crt
    SSLCertificateKeyFile $SSL_KEY_DIR/$DOMAIN.key

    # SSL安全配置
    SSLProtocol all -SSLv2 -SSLv3 -TLSv1 -TLSv1.1
    SSLCipherSuite ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
    SSLHonorCipherOrder on

    # 安全头部
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set X-Frame-Options DENY
    Header always set X-Content-Type-Options nosniff
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    <Directory $WEBROOT>
        AllowOverride All
        Require all granted
        Options Indexes FollowSymLinks
    </Directory>

    ErrorLog /var/log/httpd/ssl-website-error.log
    CustomLog /var/log/httpd/ssl-website-access.log combined
</VirtualHost>
EOF
}

# 配置Debian/Ubuntu虚拟主机
configure_debian_vhosts() {
    # HTTP重定向配置
    cat > $APACHE_CONFIG_DIR/ssl-redirect.conf << EOF
<VirtualHost *:80>
    ServerName $DOMAIN
    ServerAlias $SERVER_IP
    ServerAlias localhost

    # 重定向所有HTTP请求到HTTPS
    RewriteEngine On
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

    ErrorLog \${APACHE_LOG_DIR}/ssl-redirect-error.log
    CustomLog \${APACHE_LOG_DIR}/ssl-redirect-access.log combined
</VirtualHost>
EOF

    # HTTPS虚拟主机配置
    cat > $APACHE_CONFIG_DIR/ssl-website.conf << EOF
<VirtualHost *:443>
    ServerName $DOMAIN
    ServerAlias $SERVER_IP
    DocumentRoot $WEBROOT
    DirectoryIndex index.html index.php

    # SSL配置
    SSLEngine on
    SSLCertificateFile $SSL_CERT_DIR/$DOMAIN.crt
    SSLCertificateKeyFile $SSL_KEY_DIR/$DOMAIN.key

    # SSL安全配置
    SSLProtocol all -SSLv2 -SSLv3 -TLSv1 -TLSv1.1
    SSLCipherSuite ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
    SSLHonorCipherOrder on

    # 安全头部
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    Header always set X-Frame-Options DENY
    Header always set X-Content-Type-Options nosniff
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    <Directory $WEBROOT>
        AllowOverride All
        Require all granted
        Options Indexes FollowSymLinks
    </Directory>

    ErrorLog \${APACHE_LOG_DIR}/ssl-website-error.log
    CustomLog \${APACHE_LOG_DIR}/ssl-website-access.log combined
</VirtualHost>
EOF

    # 启用站点
    a2ensite ssl-redirect.conf
    a2ensite ssl-website.conf
}

# 创建示例网页
create_sample_website() {
    print_header "创建示例网页..."

    # 创建简单的静态主页（符合用户偏好：简单、无CSS样式）
    cat > $WEBROOT/index.html << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apache SSL Framework</title>
</head>
<body>
    <h1>Apache SSL Framework</h1>

    <h2>服务器配置信息</h2>
    <p>域名: $DOMAIN</p>
    <p>IP地址: $SERVER_IP</p>
    <p>网站根目录: $WEBROOT</p>
    <p>服务: Apache + SSL</p>
    <p>端口: 443 (HTTPS)</p>
    <p>状态: 正常运行</p>

    <h2>技术特性</h2>
    <p>Web服务器: Apache</p>
    <p>SSL/TLS: 自签名证书</p>
    <p>安全协议: TLS 1.2+</p>
    <p>字符编码: UTF-8</p>

    <h2>安全配置</h2>
    <p>HTTP重定向: 自动跳转HTTPS</p>
    <p>HSTS: 已启用</p>
    <p>X-Frame-Options: DENY</p>
    <p>X-Content-Type-Options: nosniff</p>
    <p>X-XSS-Protection: 已启用</p>

    <h2>访问信息</h2>
    <p>HTTPS访问: <a href="https://$DOMAIN">https://$DOMAIN</a></p>
    <p>IP访问: <a href="https://$SERVER_IP">https://$SERVER_IP</a></p>

</body>
</html>
EOF

    # 设置权限
    chown -R $APACHE_USER:$APACHE_USER $WEBROOT
    chmod -R 755 $WEBROOT

    print_success "示例网页创建完成"
}

# 配置防火墙
configure_firewall() {
    print_header "配置防火墙..."

    case $SYSTEM_TYPE in
        "rhel")
            # 检查firewalld状态
            if ! systemctl is-active --quiet firewalld; then
                systemctl enable firewalld
                systemctl start firewalld
                print_info "已启动firewalld服务"
            fi

            # 添加HTTP和HTTPS服务到防火墙
            firewall-cmd --permanent --add-service=http
            firewall-cmd --permanent --add-service=https
            firewall-cmd --permanent --add-port=80/tcp
            firewall-cmd --permanent --add-port=443/tcp

            # 重新加载防火墙配置
            firewall-cmd --reload
            ;;
        "debian")
            # 检查ufw状态
            if command -v ufw >/dev/null 2>&1; then
                ufw allow 80/tcp
                ufw allow 443/tcp
                print_info "已配置ufw防火墙规则"
            else
                print_info "未检测到ufw，跳过防火墙配置"
            fi
            ;;
    esac

    print_success "防火墙配置完成"
}

# 配置SELinux (仅RHEL/CentOS)
configure_selinux() {
    if [ "$SYSTEM_TYPE" != "rhel" ]; then
        return 0
    fi

    print_header "配置SELinux..."

    # 检查SELinux状态
    if command -v getenforce >/dev/null 2>&1; then
        SELINUX_STATUS=$(getenforce)
        print_info "当前SELinux状态: $SELINUX_STATUS"

        if [ "$SELINUX_STATUS" = "Enforcing" ]; then
            print_info "配置SELinux策略以支持Apache和SSL..."

            # 设置Apache相关的SELinux上下文
            setsebool -P httpd_can_network_connect on
            restorecon -R $WEBROOT/
            restorecon -R $SSL_CERT_DIR/
            restorecon -R $SSL_KEY_DIR/

            print_success "SELinux配置完成"
        fi
    else
        print_info "SELinux未安装或不可用"
    fi
}

# 启动Apache服务
start_apache() {
    print_header "启动Apache服务..."

    # 测试Apache配置
    if ! $APACHE_SERVICE -t 2>/dev/null; then
        print_error "Apache配置语法错误"
        $APACHE_SERVICE -t
        exit 1
    fi

    # 启动服务
    systemctl enable $APACHE_SERVICE
    systemctl restart $APACHE_SERVICE

    # 检查服务状态
    if systemctl is-active --quiet $APACHE_SERVICE; then
        print_success "Apache服务启动成功"
    else
        print_error "Apache服务启动失败"
        journalctl -u $APACHE_SERVICE --no-pager -l
        exit 1
    fi
}

# 测试SSL服务
test_ssl_service() {
    print_header "测试SSL服务..."

    # 等待服务完全启动
    sleep 3

    # 测试HTTP重定向
    print_info "测试HTTP重定向..."
    if curl -s -I http://localhost/ | grep -q "301\|302"; then
        print_success "HTTP重定向测试成功"
    else
        print_warning "HTTP重定向测试失败"
    fi

    # 测试HTTPS访问
    print_info "测试HTTPS访问..."
    if curl -k -s https://localhost/ | grep -q "Apache SSL Framework"; then
        print_success "HTTPS访问测试成功"
    else
        print_warning "HTTPS访问测试失败"
    fi

    # 测试SSL证书
    print_info "测试SSL证书..."
    if openssl s_client -connect localhost:443 -servername $DOMAIN < /dev/null 2>/dev/null | grep -q "Verify return code: 18"; then
        print_success "SSL证书测试成功（自签名证书）"
    else
        print_warning "SSL证书测试异常"
    fi

    # 测试端口监听
    print_info "检查端口监听状态..."
    if ss -tulnp | grep -q ":443"; then
        print_success "HTTPS端口443监听正常"
    else
        print_warning "HTTPS端口443监听异常"
    fi

    if ss -tulnp | grep -q ":80"; then
        print_success "HTTP端口80监听正常"
    else
        print_warning "HTTP端口80监听异常"
    fi

    print_success "SSL服务测试完成"
}

# 主函数
main() {
    print_header "Apache SSL框架搭建脚本"
    echo ""

    # 检查root权限
    check_root

    # 解析命令行参数
    parse_arguments "$@"

    # 显示配置信息
    show_config

    # 检测系统类型
    detect_system
    echo ""

    # 执行配置步骤
    install_apache_ssl
    echo ""
    generate_ssl_certificate
    echo ""
    configure_apache_vhosts
    echo ""
    create_sample_website
    echo ""
    configure_firewall
    echo ""
    configure_selinux
    echo ""
    start_apache
    echo ""
    test_ssl_service

    echo ""
    print_success "Apache SSL框架搭建完成!"
    print_info "域名: $DOMAIN"
    print_info "IP地址: $SERVER_IP"
    print_info "网站根目录: $WEBROOT"
    print_info "访问地址: https://$DOMAIN 或 https://$SERVER_IP"
    print_info "服务: Apache + SSL"
    print_info "端口: 443 (HTTPS), 80 (重定向到HTTPS)"
    print_info "SSL证书: 自签名证书 (浏览器会显示安全警告，这是正常的)"
    print_info ""
    print_info "配置文件位置:"
    print_info "  - SSL证书: $SSL_CERT_DIR/$DOMAIN.crt"
    print_info "  - SSL私钥: $SSL_KEY_DIR/$DOMAIN.key"
    print_info "  - Apache配置: $APACHE_CONFIG_DIR/"
}

# 执行主函数
main "$@"
