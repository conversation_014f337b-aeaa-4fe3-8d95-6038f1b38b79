#!/bin/bash

# ser3 HTTPS访问验证脚本
# 专门用于检查CentOS Server03的HTTPS配置和访问

# 设置变量
DOMAIN="nuli.cn"
IP="*************"
SSL_CERT="/etc/httpd/ssl/server.crt"
SSL_KEY="/etc/httpd/ssl/server.key"
WEB_ROOT="/var/www/html"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 检查系统基础信息
check_system_info() {
    print_header "系统基础信息检查"
    
    print_info "主机名: $(hostname)"
    print_info "IP地址: $(hostname -I | awk '{print $1}')"
    print_info "操作系统: $(cat /etc/redhat-release 2>/dev/null || echo 'Unknown')"
    print_info "当前时间: $(date)"
}

# 检查Apache服务状态
check_apache_service() {
    print_header "Apache服务状态检查"
    
    # 检查httpd服务状态
    if systemctl is-active --quiet httpd; then
        print_success "Apache服务正在运行"
        print_info "服务状态: $(systemctl is-active httpd)"
        print_info "启动状态: $(systemctl is-enabled httpd)"
    else
        print_error "Apache服务未运行"
        print_info "尝试启动Apache服务..."
        systemctl start httpd
        if systemctl is-active --quiet httpd; then
            print_success "Apache服务启动成功"
        else
            print_error "Apache服务启动失败"
            systemctl status httpd
        fi
    fi
    
    # 检查SSL模块
    if httpd -M 2>/dev/null | grep -q ssl; then
        print_success "SSL模块已加载"
    else
        print_error "SSL模块未加载"
    fi
}

# 检查端口监听
check_ports() {
    print_header "端口监听检查"
    
    # 检查80端口
    if netstat -tulnp 2>/dev/null | grep -q ":80 "; then
        print_success "端口80正在监听"
        netstat -tulnp | grep ":80 " | head -1
    else
        print_error "端口80未监听"
    fi
    
    # 检查443端口
    if netstat -tulnp 2>/dev/null | grep -q ":443 "; then
        print_success "端口443正在监听"
        netstat -tulnp | grep ":443 " | head -1
    else
        print_error "端口443未监听"
    fi
}

# 检查SSL证书
check_ssl_certificate() {
    print_header "SSL证书检查"
    
    # 检查证书文件存在性
    if [ -f "$SSL_CERT" ]; then
        print_success "SSL证书文件存在: $SSL_CERT"
        print_info "证书权限: $(stat -c%a $SSL_CERT)"
        
        # 检查证书有效性
        if openssl x509 -in "$SSL_CERT" -noout -text >/dev/null 2>&1; then
            print_success "SSL证书格式有效"
            
            # 显示证书信息
            CERT_SUBJECT=$(openssl x509 -in "$SSL_CERT" -noout -subject | sed 's/subject=//')
            CERT_ISSUER=$(openssl x509 -in "$SSL_CERT" -noout -issuer | sed 's/issuer=//')
            CERT_DATES=$(openssl x509 -in "$SSL_CERT" -noout -dates)
            
            print_info "证书主题: $CERT_SUBJECT"
            print_info "证书颁发者: $CERT_ISSUER"
            print_info "证书有效期: $CERT_DATES"
        else
            print_error "SSL证书格式无效"
        fi
    else
        print_error "SSL证书文件不存在: $SSL_CERT"
    fi
    
    # 检查私钥文件
    if [ -f "$SSL_KEY" ]; then
        print_success "SSL私钥文件存在: $SSL_KEY"
        print_info "私钥权限: $(stat -c%a $SSL_KEY)"
    else
        print_error "SSL私钥文件不存在: $SSL_KEY"
    fi
}

# 检查防火墙配置
check_firewall() {
    print_header "防火墙配置检查"
    
    if systemctl is-active --quiet firewalld; then
        print_success "防火墙服务正在运行"
        
        # 检查HTTP服务
        if firewall-cmd --list-services | grep -q http; then
            print_success "HTTP服务已允许通过防火墙"
        else
            print_warning "HTTP服务未在防火墙中开放"
        fi
        
        # 检查HTTPS服务
        if firewall-cmd --list-services | grep -q https; then
            print_success "HTTPS服务已允许通过防火墙"
        else
            print_warning "HTTPS服务未在防火墙中开放"
        fi
        
        # 显示开放的端口
        print_info "开放的服务: $(firewall-cmd --list-services)"
        print_info "开放的端口: $(firewall-cmd --list-ports)"
    else
        print_warning "防火墙服务未运行"
    fi
}

# 检查网站文件
check_website_files() {
    print_header "网站文件检查"
    
    # 检查网站根目录
    if [ -d "$WEB_ROOT" ]; then
        print_success "网站根目录存在: $WEB_ROOT"
        print_info "目录权限: $(stat -c%a $WEB_ROOT)"
        print_info "目录所有者: $(stat -c%U:%G $WEB_ROOT)"
        
        # 检查首页文件
        if [ -f "$WEB_ROOT/index.html" ]; then
            print_success "首页文件存在"
            print_info "文件大小: $(stat -c%s $WEB_ROOT/index.html) 字节"
            print_info "文件权限: $(stat -c%a $WEB_ROOT/index.html)"
        else
            print_warning "首页文件不存在，将创建测试页面"
            create_test_page
        fi
    else
        print_error "网站根目录不存在: $WEB_ROOT"
    fi
}

# 创建测试页面
create_test_page() {
    print_info "创建测试页面..."
    
    cat > "$WEB_ROOT/index.html" << EOF
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CentOS Server03 HTTPS Test</title>
</head>
<body>
    <h1>CentOS Server03 HTTPS Test Page</h1>
    
    <h2>服务器配置信息</h2>
    <p>操作系统: CentOS 9</p>
    <p>IP地址: $IP</p>
    <p>域名: $DOMAIN</p>
    <p>服务: Apache + SSL</p>
    <p>端口: 443 (HTTPS), 80 (HTTP)</p>
    <p>状态: 正常运行</p>
    
    <h2>SSL配置</h2>
    <p>证书类型: 自签名证书</p>
    <p>SSL引擎: 已启用</p>
    
    <h2>访问测试</h2>
    <p>HTTPS访问: https://$DOMAIN/</p>
    <p>IP访问: https://$IP/</p>
    
    <p>测试时间: $(date)</p>
</body>
</html>
EOF
    
    chown apache:apache "$WEB_ROOT/index.html"
    chmod 644 "$WEB_ROOT/index.html"
    print_success "测试页面创建完成"
}

# 测试HTTP访问
test_http_access() {
    print_header "HTTP访问测试"
    
    local test_urls=(
        "http://localhost/"
        "http://127.0.0.1/"
        "http://$IP/"
    )
    
    if [ "$DOMAIN" != "localhost" ] && [ "$DOMAIN" != "127.0.0.1" ]; then
        test_urls+=("http://$DOMAIN/")
    fi
    
    for url in "${test_urls[@]}"; do
        print_info "测试: $url"
        
        if command -v curl >/dev/null 2>&1; then
            local response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null)
            case $response in
                200)
                    print_success "HTTP访问成功 (状态码: $response)"
                    ;;
                301|302)
                    print_success "HTTP重定向正常 (状态码: $response)"
                    ;;
                000)
                    print_error "无法连接到服务器"
                    ;;
                *)
                    print_warning "HTTP访问异常 (状态码: $response)"
                    ;;
            esac
        else
            print_warning "curl未安装，无法测试HTTP访问"
        fi
    done
}

# 测试HTTPS访问
test_https_access() {
    print_header "HTTPS访问测试"
    
    local test_urls=(
        "https://localhost/"
        "https://127.0.0.1/"
        "https://$IP/"
    )
    
    if [ "$DOMAIN" != "localhost" ] && [ "$DOMAIN" != "127.0.0.1" ]; then
        test_urls+=("https://$DOMAIN/")
    fi
    
    for url in "${test_urls[@]}"; do
        print_info "测试: $url"
        
        if command -v curl >/dev/null 2>&1; then
            local response=$(curl -k -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null)
            case $response in
                200)
                    print_success "HTTPS访问成功 (状态码: $response)"
                    
                    # 测试页面内容
                    local content=$(curl -k -s "$url" 2>/dev/null)
                    if echo "$content" | grep -q "CentOS Server03\|HTTPS Test"; then
                        print_success "页面内容验证成功"
                    else
                        print_warning "页面内容验证失败"
                    fi
                    ;;
                000)
                    print_error "无法连接到HTTPS服务器"
                    ;;
                *)
                    print_warning "HTTPS访问异常 (状态码: $response)"
                    ;;
            esac
        else
            print_warning "curl未安装，无法测试HTTPS访问"
        fi
    done
}

# 主函数
main() {
    echo -e "${BLUE}======================================${NC}"
    echo -e "${BLUE}    ser3 HTTPS访问验证脚本${NC}"
    echo -e "${BLUE}======================================${NC}"
    
    check_system_info
    check_apache_service
    check_ports
    check_ssl_certificate
    check_firewall
    check_website_files
    test_http_access
    test_https_access
    
    print_header "验证完成"
    print_info "如果所有检查都通过，您应该能够通过以下URL访问测试页面："
    print_info "  https://$IP/"
    print_info "  https://$DOMAIN/ (如果DNS配置正确)"
    print_info "  https://localhost/ (在服务器本地)"
}

# 运行主函数
main "$@"
