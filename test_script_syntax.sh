#!/bin/bash
# 简单的语法测试脚本

echo "测试脚本语法检查..."

# 检查主脚本是否存在
if [ ! -f "centos_server03_web_ssl_config.sh" ]; then
    echo "错误: 主脚本文件不存在"
    exit 1
fi

echo "主脚本文件存在"

# 检查脚本权限
if [ ! -x "centos_server03_web_ssl_config.sh" ]; then
    echo "设置脚本执行权限..."
    chmod +x centos_server03_web_ssl_config.sh
fi

echo "脚本语法检查完成"
echo "可以使用以下命令运行:"
echo "  ./centos_server03_web_ssl_config.sh help"
echo "  ./centos_server03_web_ssl_config.sh troubleshoot"
echo "  ./centos_server03_web_ssl_config.sh"
