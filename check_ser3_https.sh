#!/bin/bash

# ser3 HTTPS快速检查脚本
# 用于快速验证CentOS Server03的HTTPS配置状态

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}======================================${NC}"
echo -e "${BLUE}    ser3 HTTPS快速检查${NC}"
echo -e "${BLUE}======================================${NC}"

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}❌ 请以root用户运行此脚本${NC}"
    echo "使用: sudo $0"
    exit 1
fi

# 设置变量
DOMAIN="nuli.cn"
IP="*************"

echo -e "\n${BLUE}=== 快速状态检查 ===${NC}"

# 1. 检查Apache服务
if systemctl is-active --quiet httpd; then
    echo -e "${GREEN}✅ Apache服务正在运行${NC}"
else
    echo -e "${RED}❌ Apache服务未运行${NC}"
    echo -e "${YELLOW}⚠️  需要先运行SSL配置脚本${NC}"
fi

# 2. 检查SSL端口
if netstat -tulnp 2>/dev/null | grep -q ":443 "; then
    echo -e "${GREEN}✅ HTTPS端口443正在监听${NC}"
else
    echo -e "${RED}❌ HTTPS端口443未监听${NC}"
fi

# 3. 检查SSL证书
if [ -f "/etc/httpd/ssl/server.crt" ]; then
    echo -e "${GREEN}✅ SSL证书文件存在${NC}"
else
    echo -e "${RED}❌ SSL证书文件不存在${NC}"
fi

# 4. 检查网站文件
if [ -f "/var/www/html/index.html" ]; then
    echo -e "${GREEN}✅ 测试网页文件存在${NC}"
else
    echo -e "${YELLOW}⚠️  测试网页文件不存在${NC}"
fi

# 5. 快速HTTPS测试
echo -e "\n${BLUE}=== HTTPS连接测试 ===${NC}"
if command -v curl >/dev/null 2>&1; then
    # 测试本地HTTPS访问
    HTTPS_RESPONSE=$(curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ 2>/dev/null)
    if [ "$HTTPS_RESPONSE" = "200" ]; then
        echo -e "${GREEN}✅ HTTPS本地访问成功 (状态码: $HTTPS_RESPONSE)${NC}"
        
        # 获取页面内容预览
        CONTENT=$(curl -k -s https://localhost/ 2>/dev/null | head -5)
        if echo "$CONTENT" | grep -q "CentOS Server03\|HTTPS Test"; then
            echo -e "${GREEN}✅ 测试页面内容正确${NC}"
        fi
    else
        echo -e "${RED}❌ HTTPS本地访问失败 (状态码: $HTTPS_RESPONSE)${NC}"
    fi
    
    # 测试IP访问
    IP_RESPONSE=$(curl -k -s -o /dev/null -w "%{http_code}" https://$IP/ 2>/dev/null)
    if [ "$IP_RESPONSE" = "200" ]; then
        echo -e "${GREEN}✅ HTTPS IP访问成功 (https://$IP/)${NC}"
    else
        echo -e "${RED}❌ HTTPS IP访问失败 (状态码: $IP_RESPONSE)${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  curl未安装，无法测试HTTPS访问${NC}"
fi

echo -e "\n${BLUE}=== 操作建议 ===${NC}"

# 检查是否需要运行配置脚本
if ! systemctl is-active --quiet httpd || ! [ -f "/etc/httpd/ssl/server.crt" ]; then
    echo -e "${YELLOW}建议操作:${NC}"
    echo "1. 运行SSL配置脚本:"
    echo "   chmod +x centos_server03_ssl_web_config.sh"
    echo "   ./centos_server03_ssl_web_config.sh"
    echo ""
    echo "2. 然后运行详细验证:"
    echo "   chmod +x verify_ser3_https_access.sh"
    echo "   ./verify_ser3_https_access.sh"
else
    echo -e "${GREEN}配置看起来正常！${NC}"
    echo "可以通过以下URL访问测试页面:"
    echo "  https://$IP/"
    echo "  https://$DOMAIN/ (如果DNS配置正确)"
    echo ""
    echo "运行详细验证:"
    echo "  chmod +x verify_ser3_https_access.sh"
    echo "  ./verify_ser3_https_access.sh"
fi

echo -e "\n${BLUE}=== 浏览器测试 ===${NC}"
echo "在浏览器中访问以下URL进行测试:"
echo "  https://$IP/"
echo "  https://$DOMAIN/"
echo ""
echo "注意: 由于使用自签名证书，浏览器会显示安全警告，"
echo "      选择'继续访问'或'接受风险'即可查看测试页面。"

echo -e "\n${BLUE}======================================${NC}"
echo -e "${BLUE}    检查完成${NC}"
echo -e "${BLUE}======================================${NC}"
