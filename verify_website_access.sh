#!/bin/bash
# 网站访问验证脚本
# 快速验证Apache SSL网站是否可以正常访问

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_DOMAIN="www.nuli.cn"
DEFAULT_IP="*************"

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "网站访问验证脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -d, --domain DOMAIN     设置域名 (默认: $DEFAULT_DOMAIN)"
    echo "  -i, --ip IP            设置IP地址 (默认: $DEFAULT_IP)"
    echo "  -h, --help             显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                     # 使用默认配置验证"
    echo "  $0 -d example.com -i **********"
    echo ""
}

# 解析命令行参数
parse_arguments() {
    DOMAIN="$DEFAULT_DOMAIN"
    SERVER_IP="$DEFAULT_IP"

    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--domain)
                DOMAIN="$2"
                shift 2
                ;;
            -i|--ip)
                SERVER_IP="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# 检查必要工具
check_tools() {
    print_header "检查必要工具..."
    
    local tools_missing=0
    
    if ! command -v curl >/dev/null 2>&1; then
        print_warning "curl未安装"
        tools_missing=1
    else
        print_success "curl可用"
    fi
    
    if ! command -v wget >/dev/null 2>&1; then
        print_warning "wget未安装"
        tools_missing=1
    else
        print_success "wget可用"
    fi
    
    if ! command -v openssl >/dev/null 2>&1; then
        print_warning "openssl未安装"
        tools_missing=1
    else
        print_success "openssl可用"
    fi
    
    if [ $tools_missing -eq 1 ]; then
        print_info "建议安装缺失的工具: dnf install -y curl wget openssl"
    fi
}

# 检查网络连通性
check_connectivity() {
    print_header "检查网络连通性..."
    
    # 检查本地连通性
    if ping -c 1 127.0.0.1 >/dev/null 2>&1; then
        print_success "本地回环连通性正常"
    else
        print_error "本地回环连通性异常"
        return 1
    fi
    
    # 检查目标IP连通性
    if ping -c 3 $SERVER_IP >/dev/null 2>&1; then
        print_success "目标IP ($SERVER_IP) 连通性正常"
    else
        print_warning "目标IP ($SERVER_IP) 连通性异常"
    fi
}

# 检查端口开放
check_ports() {
    print_header "检查端口开放状态..."
    
    # 检查80端口
    if command -v nc >/dev/null 2>&1; then
        if nc -z $SERVER_IP 80 2>/dev/null; then
            print_success "端口80开放"
        else
            print_warning "端口80未开放或无法连接"
        fi
        
        if nc -z $SERVER_IP 443 2>/dev/null; then
            print_success "端口443开放"
        else
            print_warning "端口443未开放或无法连接"
        fi
    else
        print_info "nc工具不可用，跳过端口检查"
    fi
}

# 测试HTTP访问
test_http_access() {
    print_header "测试HTTP访问..."
    
    local test_urls=(
        "http://localhost/"
        "http://127.0.0.1/"
        "http://$SERVER_IP/"
    )
    
    if [ "$DOMAIN" != "localhost" ] && [ "$DOMAIN" != "127.0.0.1" ]; then
        test_urls+=("http://$DOMAIN/")
    fi
    
    for url in "${test_urls[@]}"; do
        print_info "测试: $url"
        
        if command -v curl >/dev/null 2>&1; then
            local response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null)
            case $response in
                200)
                    print_success "HTTP访问成功 (状态码: $response)"
                    ;;
                301|302)
                    print_success "HTTP重定向正常 (状态码: $response)"
                    ;;
                000)
                    print_error "无法连接到服务器"
                    ;;
                *)
                    print_warning "HTTP访问异常 (状态码: $response)"
                    ;;
            esac
        fi
    done
}

# 测试HTTPS访问
test_https_access() {
    print_header "测试HTTPS访问..."
    
    local test_urls=(
        "https://localhost/"
        "https://127.0.0.1/"
        "https://$SERVER_IP/"
    )
    
    if [ "$DOMAIN" != "localhost" ] && [ "$DOMAIN" != "127.0.0.1" ]; then
        test_urls+=("https://$DOMAIN/")
    fi
    
    for url in "${test_urls[@]}"; do
        print_info "测试: $url"
        
        if command -v curl >/dev/null 2>&1; then
            local response=$(curl -k -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null)
            case $response in
                200)
                    print_success "HTTPS访问成功 (状态码: $response)"
                    
                    # 测试页面内容
                    local content=$(curl -k -s "$url" 2>/dev/null)
                    if echo "$content" | grep -q "CentOS Server03\|Apache SSL Framework"; then
                        print_success "页面内容验证成功"
                    else
                        print_warning "页面内容验证失败"
                    fi
                    ;;
                000)
                    print_error "无法连接到HTTPS服务器"
                    ;;
                *)
                    print_warning "HTTPS访问异常 (状态码: $response)"
                    ;;
            esac
        fi
    done
}

# 测试SSL证书
test_ssl_certificate() {
    print_header "测试SSL证书..."
    
    if ! command -v openssl >/dev/null 2>&1; then
        print_warning "openssl不可用，跳过SSL证书测试"
        return 0
    fi
    
    local test_hosts=(
        "localhost:443"
        "127.0.0.1:443"
        "$SERVER_IP:443"
    )
    
    for host in "${test_hosts[@]}"; do
        print_info "测试SSL证书: $host"
        
        local ssl_output=$(openssl s_client -connect "$host" -servername "$DOMAIN" < /dev/null 2>/dev/null)
        
        if echo "$ssl_output" | grep -q "CONNECTED"; then
            print_success "SSL连接成功"
            
            # 检查证书验证结果
            local verify_code=$(echo "$ssl_output" | grep "Verify return code" | awk '{print $4}')
            case $verify_code in
                0)
                    print_success "SSL证书验证成功（受信任证书）"
                    ;;
                18)
                    print_success "SSL证书验证成功（自签名证书）"
                    ;;
                *)
                    print_warning "SSL证书验证异常 (代码: $verify_code)"
                    ;;
            esac
            
            # 显示证书信息
            local subject=$(echo "$ssl_output" | grep "subject=" | head -1)
            local issuer=$(echo "$ssl_output" | grep "issuer=" | head -1)
            if [ -n "$subject" ]; then
                print_info "证书主题: $subject"
            fi
            if [ -n "$issuer" ]; then
                print_info "证书颁发者: $issuer"
            fi
        else
            print_error "SSL连接失败"
        fi
        
        echo ""
    done
}

# 生成访问报告
generate_report() {
    print_header "生成访问报告..."
    
    local report_file="website_access_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
网站访问验证报告
生成时间: $(date)
测试目标: $DOMAIN ($SERVER_IP)

测试结果:
- 域名: $DOMAIN
- IP地址: $SERVER_IP
- HTTP访问: 请查看上述输出
- HTTPS访问: 请查看上述输出
- SSL证书: 请查看上述输出

建议的访问地址:
- HTTPS: https://$DOMAIN
- IP访问: https://$SERVER_IP
- HTTP重定向: http://$DOMAIN → https://$DOMAIN

注意事项:
- 自签名证书会在浏览器中显示安全警告
- 确保防火墙允许80和443端口
- 确保DNS解析正确配置

详细结果请查看控制台输出。
EOF
    
    print_success "访问报告已生成: $report_file"
}

# 主函数
main() {
    print_header "网站访问验证工具"
    echo ""
    
    # 解析命令行参数
    parse_arguments "$@"
    
    print_info "验证目标: $DOMAIN ($SERVER_IP)"
    echo ""
    
    # 执行检查
    check_tools
    echo ""
    check_connectivity
    echo ""
    check_ports
    echo ""
    test_http_access
    echo ""
    test_https_access
    echo ""
    test_ssl_certificate
    echo ""
    generate_report
    
    echo ""
    print_success "网站访问验证完成!"
    print_info "如果发现问题，请运行 httpd_troubleshoot.sh 进行详细诊断"
}

# 执行主函数
main "$@"
