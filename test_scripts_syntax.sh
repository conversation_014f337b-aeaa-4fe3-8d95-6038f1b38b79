#!/bin/bash
# 脚本语法测试工具

echo "测试脚本语法..."

# 测试CentOS Server03 SSL配置脚本
echo "检查 centos_server03_ssl_web_config.sh 语法..."
if bash -n centos_server03_ssl_web_config.sh 2>/dev/null; then
    echo "✅ centos_server03_ssl_web_config.sh 语法正确"
else
    echo "❌ centos_server03_ssl_web_config.sh 语法错误"
    bash -n centos_server03_ssl_web_config.sh
fi

echo ""

# 测试通用Apache SSL框架脚本
echo "检查 apache_ssl_framework_setup.sh 语法..."
if bash -n apache_ssl_framework_setup.sh 2>/dev/null; then
    echo "✅ apache_ssl_framework_setup.sh 语法正确"
else
    echo "❌ apache_ssl_framework_setup.sh 语法错误"
    bash -n apache_ssl_framework_setup.sh
fi

echo ""
echo "语法检查完成！"
