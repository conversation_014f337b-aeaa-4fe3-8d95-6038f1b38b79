#!/bin/bash
# 快速修复DNS和SSL问题的脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# 检查是否以root用户运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请以root用户运行此脚本"
        exit 1
    fi
}

# 修复DNS问题
fix_dns_issues() {
    print_header "修复DNS服务问题..."
    
    DOMAIN="nuli.cn"
    SERVER_IP="*************"
    WEB1_IP="*************"
    WEB2_IP="*************"
    
    # 检查BIND服务状态
    if ! systemctl is-active --quiet named; then
        print_warning "BIND服务未运行，尝试启动..."
        systemctl start named
        sleep 3
    fi
    
    # 检查区域文件是否存在
    if [ ! -f "/var/named/$DOMAIN.zone" ]; then
        print_warning "DNS区域文件不存在，重新创建..."
        
        # 创建区域文件
        cat > /var/named/$DOMAIN.zone << EOF
\$TTL 86400
@   IN  SOA     dns.$DOMAIN. admin.$DOMAIN. (
        2024062001  ; Serial
        3600        ; Refresh
        1800        ; Retry
        604800      ; Expire
        86400 )     ; Minimum TTL
;
@       IN  NS      dns.$DOMAIN.
dns     IN  A       $SERVER_IP
web     IN  A       $WEB1_IP
www     IN  A       $WEB2_IP
EOF
        
        # 设置权限
        chown named:named /var/named/$DOMAIN.zone
        chmod 644 /var/named/$DOMAIN.zone
        
        print_success "DNS区域文件已重新创建"
    fi
    
    # 检查named.conf配置
    if ! grep -q "$DOMAIN" /etc/named.conf; then
        print_warning "named.conf中缺少域名配置，添加配置..."
        
        # 备份原配置
        cp /etc/named.conf /etc/named.conf.backup
        
        # 添加域名配置
        cat >> /etc/named.conf << EOF

zone "$DOMAIN" IN {
    type master;
    file "$DOMAIN.zone";
    allow-update { none; };
};
EOF
        
        print_success "已添加域名配置到named.conf"
    fi
    
    # 验证配置语法
    print_info "验证DNS配置语法..."
    if named-checkconf; then
        print_success "named.conf语法正确"
    else
        print_error "named.conf语法错误"
        return 1
    fi
    
    if named-checkzone $DOMAIN /var/named/$DOMAIN.zone; then
        print_success "区域文件语法正确"
    else
        print_error "区域文件语法错误"
        return 1
    fi
    
    # 重启DNS服务
    print_info "重启DNS服务..."
    systemctl restart named
    sleep 3
    
    if systemctl is-active --quiet named; then
        print_success "DNS服务重启成功"
    else
        print_error "DNS服务重启失败"
        journalctl -u named --no-pager -l | tail -10
        return 1
    fi
    
    # 测试DNS解析
    print_info "测试DNS解析..."
    
    if dig @127.0.0.1 dns.$DOMAIN +short | grep -q "$SERVER_IP"; then
        print_success "dns.$DOMAIN 解析正常"
    else
        print_warning "dns.$DOMAIN 解析异常"
    fi
    
    if dig @127.0.0.1 web.$DOMAIN +short | grep -q "$WEB1_IP"; then
        print_success "web.$DOMAIN 解析正常"
    else
        print_warning "web.$DOMAIN 解析异常"
    fi
    
    if dig @127.0.0.1 www.$DOMAIN +short | grep -q "$WEB2_IP"; then
        print_success "www.$DOMAIN 解析正常"
    else
        print_warning "www.$DOMAIN 解析异常"
    fi
    
    print_success "DNS问题修复完成"
}

# 修复SSL证书问题
fix_ssl_issues() {
    print_header "修复SSL证书问题..."
    
    DOMAIN="nuli.cn"
    SERVER_IP="*************"
    
    # 创建SSL证书目录
    mkdir -p /etc/pki/tls/certs
    mkdir -p /etc/pki/tls/private
    
    # 检查SSL证书是否存在
    if [ ! -f "/etc/pki/tls/certs/www.$DOMAIN.crt" ] || [ ! -s "/etc/pki/tls/certs/www.$DOMAIN.crt" ]; then
        print_warning "SSL证书不存在或为空，重新生成..."
        
        # 生成私钥
        print_info "生成SSL私钥..."
        openssl genrsa -out /etc/pki/tls/private/www.$DOMAIN.key 2048
        
        # 生成证书签名请求 (非交互式)
        print_info "生成证书签名请求..."
        openssl req -new -key /etc/pki/tls/private/www.$DOMAIN.key -out /tmp/www.$DOMAIN.csr -subj "/C=CN/ST=Beijing/L=Beijing/O=Nuli Organization/OU=IT Department/CN=www.$DOMAIN/emailAddress=admin@$DOMAIN"
        
        # 生成自签名证书
        print_info "生成自签名证书..."
        openssl x509 -req -days 365 -in /tmp/www.$DOMAIN.csr -signkey /etc/pki/tls/private/www.$DOMAIN.key -out /etc/pki/tls/certs/www.$DOMAIN.crt
        
        # 设置权限
        chmod 600 /etc/pki/tls/private/www.$DOMAIN.key
        chmod 644 /etc/pki/tls/certs/www.$DOMAIN.crt
        
        # 清理临时文件
        rm -f /tmp/www.$DOMAIN.csr
        
        print_success "SSL证书生成完成"
    else
        print_info "SSL证书已存在"
    fi
    
    # 验证证书文件
    if [ -f "/etc/pki/tls/certs/www.$DOMAIN.crt" ] && [ -s "/etc/pki/tls/certs/www.$DOMAIN.crt" ]; then
        print_success "SSL证书文件存在且不为空"
        
        # 验证证书内容
        if openssl x509 -in /etc/pki/tls/certs/www.$DOMAIN.crt -text -noout > /dev/null 2>&1; then
            print_success "SSL证书格式正确"
        else
            print_error "SSL证书格式错误"
            return 1
        fi
    else
        print_error "SSL证书文件不存在或为空"
        return 1
    fi
    
    # 检查Apache配置
    print_info "检查Apache SSL配置..."
    if httpd -t; then
        print_success "Apache配置语法正确"
    else
        print_error "Apache配置语法错误"
        return 1
    fi
    
    # 重启Apache服务
    print_info "重启Apache服务..."
    systemctl restart httpd
    sleep 3
    
    if systemctl is-active --quiet httpd; then
        print_success "Apache服务重启成功"
    else
        print_error "Apache服务重启失败"
        journalctl -u httpd --no-pager -l | tail -10
        return 1
    fi
    
    print_success "SSL问题修复完成"
}

# 主函数
main() {
    print_header "开始修复DNS和SSL问题..."
    echo ""
    
    # 检查root权限
    check_root
    
    # 检测当前服务器角色
    if [ -f "/etc/named.conf" ] || [ -d "/var/named" ]; then
        print_info "检测到DNS服务器，修复DNS问题..."
        fix_dns_issues
        echo ""
    fi
    
    if [ -d "/etc/httpd" ] && [ -d "/etc/pki/tls" ]; then
        print_info "检测到Web服务器，修复SSL问题..."
        fix_ssl_issues
        echo ""
    fi
    
    print_success "问题修复完成！"
    print_info "建议重新运行相应的配置脚本以确保所有配置正确。"
}

# 执行主函数
main "$@"
