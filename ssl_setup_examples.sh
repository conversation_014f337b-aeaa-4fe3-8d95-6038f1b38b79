#!/bin/bash
# Apache SSL框架搭建示例脚本

echo "Apache SSL框架搭建示例"
echo "======================="
echo ""

# 显示可用的脚本
echo "可用的脚本："
echo "1. centos_server03_ssl_web_config.sh - CentOS Server03专用SSL配置"
echo "2. apache_ssl_framework_setup.sh - 通用Apache SSL框架"
echo ""

# 显示使用示例
echo "使用示例："
echo ""

echo "1. CentOS Server03专用配置（固定配置）："
echo "   sudo ./centos_server03_ssl_web_config.sh"
echo ""

echo "2. 通用框架 - 默认配置："
echo "   sudo ./apache_ssl_framework_setup.sh"
echo ""

echo "3. 通用框架 - 自定义域名和IP："
echo "   sudo ./apache_ssl_framework_setup.sh -d nuli.cn -i *************"
echo ""

echo "4. 通用框架 - 完整自定义配置："
echo "   sudo ./apache_ssl_framework_setup.sh \\"
echo "     --domain example.com \\"
echo "     --ip ********** \\"
echo "     --webroot /var/www/mysite \\"
echo "     --country US \\"
echo "     --state California \\"
echo "     --city 'San Francisco' \\"
echo "     --org 'My Company' \\"
echo "     --unit 'IT Department'"
echo ""

echo "5. 查看帮助信息："
echo "   ./apache_ssl_framework_setup.sh --help"
echo ""

# 显示配置后的访问地址
echo "配置完成后的访问地址："
echo ""
echo "CentOS Server03："
echo "  - HTTPS: https://www.nuli.cn"
echo "  - IP访问: https://*************"
echo "  - HTTP重定向: http://************* → https://*************"
echo ""

echo "通用框架（示例）："
echo "  - HTTPS: https://[your-domain]"
echo "  - IP访问: https://[your-ip]"
echo "  - HTTP重定向: http://[your-domain] → https://[your-domain]"
echo ""

# 显示测试命令
echo "测试命令："
echo ""
echo "1. 测试HTTP重定向："
echo "   curl -I http://*************/"
echo ""
echo "2. 测试HTTPS访问："
echo "   curl -k -I https://*************/"
echo ""
echo "3. 测试SSL证书："
echo "   openssl s_client -connect *************:443 -servername www.nuli.cn"
echo ""
echo "4. 检查端口监听："
echo "   ss -tulnp | grep -E ':80|:443'"
echo ""
echo "5. 检查Apache状态："
echo "   systemctl status httpd"
echo ""

# 显示故障排除
echo "故障排除："
echo ""
echo "1. 检查Apache配置语法："
echo "   httpd -t"
echo ""
echo "2. 查看Apache错误日志："
echo "   journalctl -u httpd -f"
echo ""
echo "3. 检查防火墙状态："
echo "   firewall-cmd --list-all"
echo ""
echo "4. 检查SELinux状态："
echo "   getenforce"
echo "   getsebool -a | grep httpd"
echo ""

echo "注意事项："
echo "- 必须以root用户运行脚本"
echo "- 自签名证书会在浏览器中显示安全警告"
echo "- 确保防火墙允许HTTP(80)和HTTPS(443)端口"
echo "- 脚本会自动备份原始配置文件"
echo ""

echo "更多信息请查看 Apache_SSL_Setup_README.md 文档"
