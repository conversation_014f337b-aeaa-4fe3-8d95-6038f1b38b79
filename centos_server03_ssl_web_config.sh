#!/bin/bash

# 设置变量
DOMAIN="nuli.cn"
IP="*************"
DNS_IP="*************"
CERT_DIR="/etc/httpd/ssl"
SSL_CERT="$CERT_DIR/server.crt"
SSL_KEY="$CERT_DIR/server.key"

# 安装 Apache 和 OpenSSL
echo "Installing Apache and OpenSSL..."
dnf install -y httpd mod_ssl openssl

# 创建 SSL 证书目录
echo "Creating SSL certificate directory..."
mkdir -p $CERT_DIR

# 生成自签名证书
echo "Generating self-signed SSL certificate..."
openssl genpkey -algorithm RSA -out $SSL_KEY -pkeyopt rsa_keygen_bits:2048
openssl req -new -key $SSL_KEY -out $CERT_DIR/server.csr -subj "/C=CN/ST=Zhejiang/L=Hangzhou/O=Example/OU=IT Department/CN=$DOMAIN"
openssl x509 -req -days 365 -in $CERT_DIR/server.csr -signkey $SSL_KEY -out $SSL_CERT

# 配置 Apache SSL 虚拟主机
echo "Configuring Apache SSL VirtualHost..."
cat <<EOL > /etc/httpd/conf.d/ssl.conf
<VirtualHost *:443>
    ServerAdmin webmaster@$DOMAIN
    DocumentRoot "/var/www/html"
    ServerName $DOMAIN

    SSLEngine on
    SSLCertificateFile $SSL_CERT
    SSLCertificateKeyFile $SSL_KEY

    <Directory "/var/www/html">
        AllowOverride All
        Require all granted
    </Directory>

    ErrorLog logs/ssl_error_log
    CustomLog logs/ssl_access_log combined

</VirtualHost>
EOL

# 配置 Apache HTTP 虚拟主机（HTTP）
echo "Configuring Apache HTTP VirtualHost..."
cat <<EOL > /etc/httpd/conf.d/nuli.conf
<VirtualHost *:80>
    ServerAdmin webmaster@$DOMAIN
    DocumentRoot "/var/www/html"
    ServerName $DOMAIN

    ErrorLog logs/error_log
    CustomLog logs/access_log combined

</VirtualHost>
EOL

# 配置防火墙允许 80 和 443 端口
echo "Configuring firewall..."
firewall-cmd --zone=public --add-service=http --permanent
firewall-cmd --zone=public --add-service=https --permanent
firewall-cmd --reload

# 配置 DNS 解析
echo "Configuring DNS resolution..."
echo "$IP $DOMAIN" >> /etc/hosts

# 启动和启用 Apache 服务
echo "Starting and enabling Apache..."
systemctl start httpd
systemctl enable httpd

# 启用 SSL 模块
echo "Enabling SSL module..."
systemctl restart httpd

# 输出 Apache 服务状态
echo "Apache and SSL configuration complete. Checking Apache status..."
systemctl status httpd

echo "Self-signed certificate generated and Apache configured for $DOMAIN with SSL."
