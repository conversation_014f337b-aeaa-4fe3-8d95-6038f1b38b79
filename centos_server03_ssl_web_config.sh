#!/bin/bash

# 设置变量
DOMAIN="nuli.cn"
IP="*************"
DNS_IP="*************"
CERT_DIR="/etc/httpd/ssl"
SSL_CERT="$CERT_DIR/server.crt"
SSL_KEY="$CERT_DIR/server.key"

# 安装 Apache 和 OpenSSL
echo "Installing Apache and OpenSSL..."
dnf install -y httpd mod_ssl openssl

# 创建 SSL 证书目录
echo "Creating SSL certificate directory..."
mkdir -p $CERT_DIR

# 生成自签名证书
echo "Generating self-signed SSL certificate..."
openssl genpkey -algorithm RSA -out $SSL_KEY -pkeyopt rsa_keygen_bits:2048
openssl req -new -key $SSL_KEY -out $CERT_DIR/server.csr -subj "/C=CN/ST=Zhejiang/L=Hangzhou/O=Example/OU=IT Department/CN=$DOMAIN"
openssl x509 -req -days 365 -in $CERT_DIR/server.csr -signkey $SSL_KEY -out $SSL_CERT

# 配置 Apache SSL 虚拟主机
echo "Configuring Apache SSL VirtualHost..."
cat <<EOL > /etc/httpd/conf.d/ssl.conf
<VirtualHost *:443>
    ServerAdmin webmaster@$DOMAIN
    DocumentRoot "/var/www/html"
    ServerName $DOMAIN

    SSLEngine on
    SSLCertificateFile $SSL_CERT
    SSLCertificateKeyFile $SSL_KEY

    <Directory "/var/www/html">
        AllowOverride All
        Require all granted
    </Directory>

    ErrorLog logs/ssl_error_log
    CustomLog logs/ssl_access_log combined

</VirtualHost>
EOL

# 配置 Apache HTTP 虚拟主机（HTTP）
echo "Configuring Apache HTTP VirtualHost..."
cat <<EOL > /etc/httpd/conf.d/nuli.conf
<VirtualHost *:80>
    ServerAdmin webmaster@$DOMAIN
    DocumentRoot "/var/www/html"
    ServerName $DOMAIN

    ErrorLog logs/error_log
    CustomLog logs/access_log combined

</VirtualHost>
EOL

# 配置防火墙允许 80 和 443 端口
echo "Configuring firewall..."
firewall-cmd --zone=public --add-service=http --permanent
firewall-cmd --zone=public --add-service=https --permanent
firewall-cmd --reload

# 配置 DNS 解析
echo "Configuring DNS resolution..."
echo "$IP $DOMAIN" >> /etc/hosts

# 启动和启用 Apache 服务
echo "Starting and enabling Apache..."
systemctl start httpd
systemctl enable httpd

# 启用 SSL 模块
echo "Enabling SSL module..."
systemctl restart httpd

# 创建测试网页
echo "Creating test web page..."
cat <<EOL > /var/www/html/index.html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CentOS Server03 SSL Test</title>
</head>
<body>
    <h1>CentOS Server03 SSL Test Page</h1>

    <h2>服务器配置信息</h2>
    <p>操作系统: CentOS 9</p>
    <p>IP地址: $IP</p>
    <p>域名: $DOMAIN</p>
    <p>服务: Apache + SSL</p>
    <p>端口: 443 (HTTPS), 80 (HTTP)</p>
    <p>状态: 正常运行</p>

    <h2>SSL配置</h2>
    <p>证书类型: 自签名证书</p>
    <p>证书位置: $SSL_CERT</p>
    <p>私钥位置: $SSL_KEY</p>
    <p>SSL引擎: 已启用</p>

    <h2>访问测试</h2>
    <p>HTTP访问: http://$DOMAIN/</p>
    <p>HTTPS访问: https://$DOMAIN/</p>
    <p>IP访问: https://$IP/</p>

    <p>测试时间: $(date)</p>
</body>
</html>
EOL

# 设置正确的权限
chown apache:apache /var/www/html/index.html
chmod 644 /var/www/html/index.html

# 输出 Apache 服务状态
echo "Apache and SSL configuration complete. Checking Apache status..."
systemctl status httpd

echo "Self-signed certificate generated and Apache configured for $DOMAIN with SSL."
echo "Test web page created at /var/www/html/index.html"
