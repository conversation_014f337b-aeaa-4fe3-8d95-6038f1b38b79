# CentOS服务器手动测试指南

## 📋 测试环境概述

### 服务器配置
- **DNS服务器 (Server01)**: ************* → dns.nuli.cn
- **WEB服务器1 (Server02)**: ************* → web.nuli.cn
- **WEB服务器2 (Server03)**: ************* → www.nuli.cn
- **域名**: nuli.cn

### 测试前准备
1. 确保所有服务器已按脚本配置完成
2. 确保客户端DNS已配置指向*************
3. 准备测试工具：curl, dig, nslookup, ping, telnet

---

## 🔍 1. DNS服务测试

### 1.1 DNS服务状态检查
```bash
# 在DNS服务器上检查BIND服务状态
systemctl status named
systemctl is-active named

# 检查DNS端口监听
netstat -tulnp | grep :53
ss -tulnp | grep :53
```

### 1.2 DNS解析测试
```bash
# 测试本地DNS解析
dig @************* dns.nuli.cn
dig @************* web.nuli.cn
dig @************* www.nuli.cn

# 使用nslookup测试
nslookup dns.nuli.cn *************
nslookup web.nuli.cn *************
nslookup www.nuli.cn *************

# 测试反向解析
dig @************* -x *************
dig @************* -x *************
dig @************* -x *************
```

### 1.3 客户端DNS配置测试
```bash
# 检查客户端DNS配置
cat /etc/resolv.conf
nmcli connection show | grep -v UUID

# 测试域名解析
ping -c 3 dns.nuli.cn
ping -c 3 web.nuli.cn
ping -c 3 www.nuli.cn

# 检查hosts文件备用配置
cat /etc/hosts | grep nuli.cn
```

**预期结果:**
- ✅ dns.nuli.cn → *************
- ✅ web.nuli.cn → *************
- ✅ www.nuli.cn → *************

---

## 🌐 2. WEB服务器1 (Server02) 测试

### 2.1 Apache服务状态检查
```bash
# 检查Apache服务状态
systemctl status httpd
systemctl is-active httpd

# 检查端口监听
netstat -tulnp | grep :80
ss -tulnp | grep :80

# 检查Apache配置
httpd -t
httpd -S
```

### 2.2 HTTP访问测试
```bash
# 基本HTTP访问测试
curl -I http://*************/
curl -s http://*************/ | head -20

# 域名访问测试
curl -I http://web.nuli.cn/
curl -s http://web.nuli.cn/ | head -20

# 测试响应时间
time curl -s http://*************/ > /dev/null
```

### 2.3 MariaDB数据库测试
```bash
# 检查MariaDB服务状态
systemctl status mariadb
systemctl is-active mariadb

# 检查数据库端口
netstat -tulnp | grep :3306
ss -tulnp | grep :3306

# 连接数据库测试
mysql -u root -p
# 输入密码后执行：
SHOW DATABASES;
USE wordpress;
SHOW TABLES;
SELECT USER();
EXIT;

# 测试user1用户
mysql -u user1 -p123456
# 执行：
SHOW DATABASES;
EXIT;
```

### 2.4 WordPress功能测试
```bash
# 检查WordPress文件
ls -la /var/www/website/
ls -la /var/www/website/wp-config.php

# 检查WordPress数据库连接
curl -s http://*************/wp-admin/install.php | grep -i "wordpress"

# 测试WordPress管理后台访问
curl -I http://*************/wp-admin/
```

**预期结果:**
- ✅ Apache服务正常运行
- ✅ HTTP访问返回静态页面
- ✅ MariaDB服务正常
- ✅ WordPress文件完整
- ✅ 数据库连接正常

---

## 🔒 3. WEB服务器2 (Server03) SSL测试

### 3.1 Apache SSL服务检查
```bash
# 检查Apache服务状态
systemctl status httpd
systemctl is-active httpd

# 检查SSL端口监听
netstat -tulnp | grep :443
ss -tulnp | grep :443

# 检查SSL模块
httpd -M | grep ssl
```

### 3.2 SSL证书测试
```bash
# 检查SSL证书文件
ls -la /etc/pki/tls/certs/www.nuli.cn.crt
ls -la /etc/pki/tls/private/www.nuli.cn.key

# 查看证书详细信息
openssl x509 -in /etc/pki/tls/certs/www.nuli.cn.crt -text -noout

# 验证证书和私钥匹配
openssl x509 -noout -modulus -in /etc/pki/tls/certs/www.nuli.cn.crt | openssl md5
openssl rsa -noout -modulus -in /etc/pki/tls/private/www.nuli.cn.key | openssl md5
```

### 3.3 HTTPS访问测试
```bash
# HTTP重定向测试
curl -I http://*************/
curl -I http://www.nuli.cn/

# HTTPS访问测试
curl -k -I https://*************/
curl -k -s https://*************/ | head -20

# 域名HTTPS访问
curl -k -I https://www.nuli.cn/
curl -k -s https://www.nuli.cn/ | head -20

# SSL握手测试
openssl s_client -connect *************:443 -servername www.nuli.cn < /dev/null

# 测试SSL安全头部
curl -k -I https://*************/ | grep -i "strict-transport-security"
curl -k -I https://*************/ | grep -i "x-frame-options"
```

### 3.4 SSL性能测试
```bash
# 测试SSL连接时间
time openssl s_client -connect *************:443 < /dev/null

# 测试HTTPS响应时间
time curl -k -s https://*************/ > /dev/null
```

**预期结果:**
- ✅ HTTP自动重定向到HTTPS (301/302)
- ✅ HTTPS访问正常返回静态页面
- ✅ SSL证书有效
- ✅ 安全头部配置正确

---

## 🔥 4. 防火墙测试

### 4.1 防火墙状态检查
```bash
# 检查firewalld状态
systemctl status firewalld
firewall-cmd --state

# 查看防火墙规则
firewall-cmd --list-all
firewall-cmd --list-services
firewall-cmd --list-ports
```

### 4.2 端口访问测试
```bash
# 测试DNS端口 (53)
telnet ************* 53
nc -zv ************* 53

# 测试HTTP端口 (80)
telnet ************* 80
telnet ************* 80
nc -zv ************* 80

# 测试HTTPS端口 (443)
telnet ************* 443
nc -zv ************* 443

# 测试MySQL端口 (3306) - 应该只允许本地访问
telnet ************* 3306
```

**预期结果:**
- ✅ DNS端口53开放
- ✅ HTTP端口80开放
- ✅ HTTPS端口443开放
- ✅ MySQL端口3306仅本地访问

---

## 🛡️ 5. SELinux测试

### 5.1 SELinux状态检查
```bash
# 检查SELinux状态
getenforce
sestatus

# 查看SELinux布尔值
getsebool -a | grep httpd
```

### 5.2 SELinux上下文检查
```bash
# 检查Web目录上下文
ls -laZ /var/www/
ls -laZ /var/www/website/
ls -laZ /var/www/yuanchu/

# 检查SSL证书上下文
ls -laZ /etc/pki/tls/certs/
ls -laZ /etc/pki/tls/private/
```

**预期结果:**
- ✅ SELinux处于Enforcing模式
- ✅ Web目录具有正确的SELinux上下文
- ✅ httpd相关布尔值正确设置

---

## 📊 6. 综合性能测试

### 6.1 网络连通性测试
```bash
# 测试服务器间连通性
ping -c 5 *************
ping -c 5 *************
ping -c 5 *************

# 测试网络延迟
ping -c 10 ************* | tail -1
ping -c 10 ************* | tail -1
ping -c 10 ************* | tail -1
```

### 6.2 服务响应时间测试
```bash
# DNS查询响应时间
time dig @************* web.nuli.cn

# HTTP响应时间
time curl -s http://*************/ > /dev/null

# HTTPS响应时间
time curl -k -s https://*************/ > /dev/null
```

### 6.3 并发访问测试
```bash
# 简单并发HTTP测试
for i in {1..10}; do
  curl -s http://*************/ > /dev/null &
done
wait

# 简单并发HTTPS测试
for i in {1..10}; do
  curl -k -s https://*************/ > /dev/null &
done
wait
```

---

## 🔧 7. 故障排除指南

### 7.1 常见问题检查
```bash
# 检查系统日志
journalctl -u named -f
journalctl -u httpd -f
journalctl -u mariadb -f

# 检查Apache错误日志
tail -f /var/log/httpd/error_log
tail -f /var/log/httpd/website-error.log
tail -f /var/log/httpd/www-ssl-error.log

# 检查系统资源
top
free -h
df -h
```

### 7.2 服务重启命令
```bash
# 重启DNS服务
systemctl restart named

# 重启Web服务
systemctl restart httpd

# 重启数据库服务
systemctl restart mariadb

# 重新加载防火墙
firewall-cmd --reload
```

---

## ✅ 8. 测试检查清单

### DNS服务器 (*************)
- [ ] BIND服务正常运行
- [ ] 端口53监听正常
- [ ] 域名解析正确
- [ ] 防火墙规则正确
- [ ] SELinux配置正确

### WEB服务器1 (*************)
- [ ] Apache服务正常运行
- [ ] HTTP访问正常
- [ ] MariaDB服务正常
- [ ] WordPress安装完整
- [ ] 数据库连接正常
- [ ] 防火墙规则正确

### WEB服务器2 (*************)
- [ ] Apache服务正常运行
- [ ] SSL证书有效
- [ ] HTTP重定向正常
- [ ] HTTPS访问正常
- [ ] 安全头部配置正确
- [ ] 防火墙规则正确

### 客户端配置
- [ ] DNS配置正确
- [ ] 域名解析正常
- [ ] hosts文件备用配置
- [ ] 网络连通性正常

---

## 📝 测试报告模板

### 测试环境
- 测试日期: ___________
- 测试人员: ___________
- 系统版本: CentOS 9

### 测试结果
| 测试项目 | 预期结果 | 实际结果 | 状态 | 备注 |
|---------|---------|---------|------|------|
| DNS解析 | 正常 | _____ | ✅/❌ | _____ |
| HTTP访问 | 正常 | _____ | ✅/❌ | _____ |
| HTTPS访问 | 正常 | _____ | ✅/❌ | _____ |
| SSL证书 | 有效 | _____ | ✅/❌ | _____ |
| 数据库连接 | 正常 | _____ | ✅/❌ | _____ |
| 防火墙 | 正常 | _____ | ✅/❌ | _____ |

### 问题记录
1. _______________
2. _______________
3. _______________

### 建议改进
1. _______________
2. _______________
3. _______________

---

## 🚀 9. 高级测试场景

### 9.1 负载测试
```bash
# 使用ab工具进行HTTP负载测试
ab -n 100 -c 10 http://*************/

# 使用ab工具进行HTTPS负载测试
ab -n 100 -c 10 -k https://*************/

# 使用curl进行DNS负载测试
for i in {1..100}; do
  dig @************* web.nuli.cn > /dev/null &
done
wait
```

### 9.2 安全测试
```bash
# 测试SSL协议版本
nmap --script ssl-enum-ciphers -p 443 *************

# 测试HTTP安全头部
curl -k -I https://*************/ | grep -E "(X-|Strict|Content-Security)"

# 测试SQL注入防护 (WordPress)
curl -s "http://*************/?id=1' OR '1'='1" | grep -i error

# 端口扫描测试
nmap -sS *************
nmap -sS *************
nmap -sS *************
```

### 9.3 故障恢复测试
```bash
# 模拟DNS服务故障
systemctl stop named
# 测试客户端是否使用hosts文件
ping dns.nuli.cn
# 恢复服务
systemctl start named

# 模拟Web服务故障
systemctl stop httpd
curl -I http://*************/
# 恢复服务
systemctl start httpd

# 模拟数据库故障
systemctl stop mariadb
mysql -u root -p
# 恢复服务
systemctl start mariadb
```

### 9.4 配置验证测试
```bash
# 验证Apache配置语法
httpd -t

# 验证DNS配置语法
named-checkconf
named-checkzone nuli.cn /var/named/nuli.cn.zone

# 验证SSL证书链
openssl verify /etc/pki/tls/certs/www.nuli.cn.crt

# 验证防火墙规则
iptables -L -n
firewall-cmd --list-all-zones
```

---

## 🔍 10. 监控和日志分析

### 10.1 实时监控
```bash
# 实时查看Apache访问日志
tail -f /var/log/httpd/access_log

# 实时查看DNS查询日志
tail -f /var/log/messages | grep named

# 实时查看系统资源
watch -n 1 'free -h && echo && df -h && echo && netstat -tuln'

# 监控网络连接
watch -n 2 'netstat -an | grep -E ":80|:443|:53|:3306"'
```

### 10.2 日志分析
```bash
# 分析Apache访问日志
awk '{print $1}' /var/log/httpd/access_log | sort | uniq -c | sort -nr
awk '{print $7}' /var/log/httpd/access_log | sort | uniq -c | sort -nr

# 分析DNS查询日志
grep "query:" /var/log/messages | awk '{print $NF}' | sort | uniq -c

# 分析错误日志
grep -i error /var/log/httpd/error_log | tail -20
grep -i error /var/log/messages | grep named | tail -20
```

### 10.3 性能指标收集
```bash
# 收集系统性能数据
iostat 1 5
vmstat 1 5
sar -u 1 5

# 收集网络性能数据
iftop -i eth0
nethogs

# 收集Apache性能数据
curl -s http://*************/server-status | grep -E "requests|CPU"
```

---

## 🛠️ 11. 自动化测试脚本

### 11.1 快速健康检查脚本
```bash
#!/bin/bash
# 创建文件: health_check.sh

echo "=== CentOS服务器健康检查 ==="
echo "检查时间: $(date)"
echo

# DNS服务检查
echo "1. DNS服务检查:"
if systemctl is-active --quiet named; then
    echo "✅ DNS服务运行正常"
    if dig @************* web.nuli.cn +short | grep -q "*************"; then
        echo "✅ DNS解析正常"
    else
        echo "❌ DNS解析异常"
    fi
else
    echo "❌ DNS服务未运行"
fi
echo

# Web服务器1检查
echo "2. Web服务器1检查:"
if curl -s http://*************/ | grep -q "CentOS Server02"; then
    echo "✅ Web服务器1正常"
else
    echo "❌ Web服务器1异常"
fi

if systemctl is-active --quiet mariadb; then
    echo "✅ MariaDB服务正常"
else
    echo "❌ MariaDB服务异常"
fi
echo

# Web服务器2检查
echo "3. Web服务器2检查:"
if curl -k -s https://*************/ | grep -q "CentOS Server03"; then
    echo "✅ Web服务器2 HTTPS正常"
else
    echo "❌ Web服务器2 HTTPS异常"
fi

if curl -I http://*************/ 2>/dev/null | grep -q "301\|302"; then
    echo "✅ HTTP重定向正常"
else
    echo "❌ HTTP重定向异常"
fi
echo

echo "=== 健康检查完成 ==="
```

### 11.2 性能测试脚本
```bash
#!/bin/bash
# 创建文件: performance_test.sh

echo "=== 性能测试开始 ==="

# DNS性能测试
echo "1. DNS查询性能测试:"
time_dns=$(time (dig @************* web.nuli.cn > /dev/null) 2>&1 | grep real | awk '{print $2}')
echo "DNS查询时间: $time_dns"

# HTTP性能测试
echo "2. HTTP访问性能测试:"
time_http=$(time (curl -s http://*************/ > /dev/null) 2>&1 | grep real | awk '{print $2}')
echo "HTTP响应时间: $time_http"

# HTTPS性能测试
echo "3. HTTPS访问性能测试:"
time_https=$(time (curl -k -s https://*************/ > /dev/null) 2>&1 | grep real | awk '{print $2}')
echo "HTTPS响应时间: $time_https"

# 并发测试
echo "4. 并发访问测试:"
start_time=$(date +%s)
for i in {1..20}; do
    curl -s http://*************/ > /dev/null &
done
wait
end_time=$(date +%s)
echo "20个并发请求完成时间: $((end_time - start_time))秒"

echo "=== 性能测试完成 ==="
```

---

## 📱 12. 浏览器测试指南

### 12.1 基本功能测试
1. **DNS解析测试**
   - 在浏览器地址栏输入: `http://web.nuli.cn`
   - 预期: 显示Server02静态页面

2. **HTTPS访问测试**
   - 在浏览器地址栏输入: `https://www.nuli.cn`
   - 预期: 显示安全警告，点击"继续访问"后显示Server03静态页面

3. **HTTP重定向测试**
   - 在浏览器地址栏输入: `http://www.nuli.cn`
   - 预期: 自动重定向到HTTPS版本

### 12.2 SSL证书测试
1. 访问 `https://www.nuli.cn`
2. 点击地址栏的锁图标
3. 查看证书详细信息
4. 验证证书主题名称为 `www.nuli.cn`

### 12.3 WordPress测试
1. 访问 `http://web.nuli.cn/wp-admin/`
2. 应该显示WordPress安装页面
3. 按照向导完成WordPress安装
4. 测试WordPress管理功能

---

## 🔧 13. 故障排除命令速查

### 13.1 服务状态检查
```bash
# 检查所有关键服务
systemctl status named httpd mariadb firewalld

# 检查端口监听
ss -tulnp | grep -E ":53|:80|:443|:3306"

# 检查进程
ps aux | grep -E "named|httpd|mysqld"
```

### 13.2 网络诊断
```bash
# 网络连通性
ping -c 3 *************
ping -c 3 *************
ping -c 3 *************

# 路由检查
traceroute *************
ip route show

# DNS解析检查
nslookup web.nuli.cn
dig web.nuli.cn
```

### 13.3 日志查看
```bash
# 系统日志
journalctl -f
journalctl -u named -f
journalctl -u httpd -f

# 应用日志
tail -f /var/log/httpd/error_log
tail -f /var/log/httpd/access_log
tail -f /var/log/messages
```

---

**测试完成后，请确保所有服务正常运行，并记录任何发现的问题。建议定期执行这些测试以确保系统稳定性。**
