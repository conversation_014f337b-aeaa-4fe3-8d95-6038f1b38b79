#!/bin/bash
# Apache httpd 故障排除脚本
# 用于诊断和修复Apache服务器问题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[HEADER]${NC} $1"
}

# 检查httpd安装状态
check_httpd_installation() {
    print_header "检查httpd安装状态..."
    
    if command -v httpd >/dev/null 2>&1; then
        print_success "httpd已安装"
        print_info "httpd版本: $(httpd -v | head -1)"
    else
        print_error "httpd未安装"
        print_info "安装命令: dnf install -y httpd"
        return 1
    fi
    
    if command -v systemctl >/dev/null 2>&1; then
        print_success "systemctl可用"
    else
        print_error "systemctl不可用"
        return 1
    fi
}

# 检查httpd服务状态
check_httpd_service() {
    print_header "检查httpd服务状态..."
    
    # 检查服务状态
    if systemctl is-active --quiet httpd; then
        print_success "httpd服务正在运行"
    else
        print_warning "httpd服务未运行"
    fi
    
    if systemctl is-enabled --quiet httpd; then
        print_success "httpd服务已启用开机自启"
    else
        print_warning "httpd服务未启用开机自启"
    fi
    
    # 显示详细状态
    print_info "httpd服务详细状态:"
    systemctl status httpd --no-pager -l | head -15
}

# 检查httpd配置
check_httpd_config() {
    print_header "检查httpd配置..."
    
    # 测试配置语法
    print_info "测试Apache配置语法..."
    if httpd -t; then
        print_success "Apache配置语法正确"
    else
        print_error "Apache配置语法错误"
        return 1
    fi
    
    # 检查主配置文件
    if [ -f "/etc/httpd/conf/httpd.conf" ]; then
        print_success "主配置文件存在"
    else
        print_error "主配置文件不存在"
        return 1
    fi
    
    # 检查SSL配置
    if [ -f "/etc/httpd/conf.d/ssl.conf" ]; then
        print_success "SSL配置文件存在"
    else
        print_warning "SSL配置文件不存在"
    fi
    
    # 检查自定义配置
    print_info "检查自定义配置文件:"
    ls -la /etc/httpd/conf.d/*.conf 2>/dev/null || print_info "无自定义配置文件"
}

# 检查端口监听
check_ports() {
    print_header "检查端口监听..."
    
    # 检查80端口
    if ss -tulnp | grep -q ":80"; then
        print_success "端口80正在监听"
        ss -tulnp | grep ":80"
    else
        print_warning "端口80未监听"
    fi
    
    # 检查443端口
    if ss -tulnp | grep -q ":443"; then
        print_success "端口443正在监听"
        ss -tulnp | grep ":443"
    else
        print_warning "端口443未监听"
    fi
    
    # 检查端口占用
    print_info "检查可能的端口冲突:"
    netstat -tulnp | grep -E ":80|:443" 2>/dev/null || print_info "无端口冲突"
}

# 检查防火墙
check_firewall() {
    print_header "检查防火墙设置..."
    
    if systemctl is-active --quiet firewalld; then
        print_success "firewalld正在运行"
        
        # 检查HTTP服务
        if firewall-cmd --list-services | grep -q http; then
            print_success "HTTP服务已允许"
        else
            print_warning "HTTP服务未允许"
            print_info "添加命令: firewall-cmd --permanent --add-service=http"
        fi
        
        # 检查HTTPS服务
        if firewall-cmd --list-services | grep -q https; then
            print_success "HTTPS服务已允许"
        else
            print_warning "HTTPS服务未允许"
            print_info "添加命令: firewall-cmd --permanent --add-service=https"
        fi
        
        # 显示当前规则
        print_info "当前防火墙规则:"
        firewall-cmd --list-all
    else
        print_info "firewalld未运行"
    fi
}

# 检查SELinux
check_selinux() {
    print_header "检查SELinux设置..."
    
    if command -v getenforce >/dev/null 2>&1; then
        SELINUX_STATUS=$(getenforce)
        print_info "SELinux状态: $SELINUX_STATUS"
        
        if [ "$SELINUX_STATUS" = "Enforcing" ]; then
            # 检查httpd相关的SELinux布尔值
            print_info "检查httpd相关的SELinux布尔值:"
            getsebool -a | grep httpd | head -10
            
            # 检查文件上下文
            print_info "检查网站目录SELinux上下文:"
            ls -laZ /var/www/ 2>/dev/null | head -5
        fi
    else
        print_info "SELinux未安装"
    fi
}

# 检查网站文件
check_website_files() {
    print_header "检查网站文件..."
    
    # 检查默认网站目录
    if [ -d "/var/www/html" ]; then
        print_success "默认网站目录存在"
        print_info "目录权限: $(stat -c%a /var/www/html)"
        print_info "目录所有者: $(stat -c%U:%G /var/www/html)"
    else
        print_warning "默认网站目录不存在"
    fi
    
    # 检查自定义网站目录
    if [ -d "/var/www/yuanchu" ]; then
        print_success "自定义网站目录存在"
        print_info "目录权限: $(stat -c%a /var/www/yuanchu)"
        print_info "目录所有者: $(stat -c%U:%G /var/www/yuanchu)"
        
        if [ -f "/var/www/yuanchu/index.html" ]; then
            print_success "网站首页文件存在"
            print_info "文件大小: $(stat -c%s /var/www/yuanchu/index.html) 字节"
        else
            print_warning "网站首页文件不存在"
        fi
    else
        print_info "自定义网站目录不存在"
    fi
}

# 检查SSL证书
check_ssl_certificates() {
    print_header "检查SSL证书..."
    
    SSL_CERT_DIR="/etc/pki/tls/certs"
    SSL_KEY_DIR="/etc/pki/tls/private"
    
    # 检查证书目录
    if [ -d "$SSL_CERT_DIR" ]; then
        print_success "SSL证书目录存在"
        print_info "证书文件:"
        ls -la $SSL_CERT_DIR/*.crt 2>/dev/null || print_info "无证书文件"
    else
        print_warning "SSL证书目录不存在"
    fi
    
    # 检查私钥目录
    if [ -d "$SSL_KEY_DIR" ]; then
        print_success "SSL私钥目录存在"
        print_info "私钥文件:"
        ls -la $SSL_KEY_DIR/*.key 2>/dev/null || print_info "无私钥文件"
    else
        print_warning "SSL私钥目录不存在"
    fi
    
    # 验证证书
    for cert in $SSL_CERT_DIR/*.crt; do
        if [ -f "$cert" ]; then
            print_info "验证证书: $cert"
            if openssl x509 -in "$cert" -text -noout >/dev/null 2>&1; then
                print_success "证书格式正确"
                # 显示证书信息
                openssl x509 -in "$cert" -subject -issuer -dates -noout
            else
                print_error "证书格式错误"
            fi
        fi
    done
}

# 测试网站访问
test_website_access() {
    print_header "测试网站访问..."
    
    # 测试HTTP访问
    if command -v curl >/dev/null 2>&1; then
        print_info "测试HTTP访问..."
        HTTP_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/ 2>/dev/null)
        print_info "HTTP响应码: $HTTP_RESPONSE"
        
        print_info "测试HTTPS访问..."
        HTTPS_RESPONSE=$(curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ 2>/dev/null)
        print_info "HTTPS响应码: $HTTPS_RESPONSE"
        
        # 测试页面内容
        if curl -k -s https://localhost/ | head -5; then
            print_success "可以获取页面内容"
        else
            print_warning "无法获取页面内容"
        fi
    else
        print_warning "curl未安装，无法测试网站访问"
    fi
}

# 查看日志
check_logs() {
    print_header "检查Apache日志..."
    
    # 检查错误日志
    if [ -f "/var/log/httpd/error_log" ]; then
        print_info "最近的错误日志:"
        tail -10 /var/log/httpd/error_log
    else
        print_warning "错误日志文件不存在"
    fi
    
    # 检查访问日志
    if [ -f "/var/log/httpd/access_log" ]; then
        print_info "最近的访问日志:"
        tail -5 /var/log/httpd/access_log
    else
        print_warning "访问日志文件不存在"
    fi
    
    # 检查systemd日志
    print_info "systemd日志:"
    journalctl -u httpd --no-pager -l | tail -10
}

# 主函数
main() {
    print_header "Apache httpd 故障排除工具"
    echo ""
    
    check_httpd_installation
    echo ""
    check_httpd_service
    echo ""
    check_httpd_config
    echo ""
    check_ports
    echo ""
    check_firewall
    echo ""
    check_selinux
    echo ""
    check_website_files
    echo ""
    check_ssl_certificates
    echo ""
    test_website_access
    echo ""
    check_logs
    
    echo ""
    print_success "故障排除检查完成!"
    print_info "如果发现问题，请根据上述信息进行修复"
}

# 执行主函数
main "$@"
